#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增量模式修复
验证CompletePosition对象转换为PositionData对象的修复
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import pandas as pd
from datetime import datetime, timedelta
from modules.contract_risk_analysis.optimizers.position_based_optimizer import CompletePosition
from modules.user_analysis.models.user_behavior_models import PositionData

def test_complete_position_to_position_data_conversion():
    """测试CompletePosition到PositionData的转换"""
    print("🧪 测试CompletePosition到PositionData的转换...")
    
    # 创建一个CompletePosition对象（模拟增量模式中的数据）
    complete_position = CompletePosition(
        position_id="test_pos_001",
        member_id="test_user_001",
        contract_name="BTCUSDT",
        
        # 开仓信息 - 注意这里使用的是first_open_time，不是open_time
        first_open_time=datetime.now() - timedelta(hours=1),
        last_open_time=datetime.now() - timedelta(minutes=50),
        total_open_amount=1000.0,
        total_open_volume=0.02,
        avg_open_price=50000.0,
        open_trades_count=2,
        primary_side=1,
        
        # 平仓信息
        first_close_time=datetime.now() - timedelta(minutes=10),
        last_close_time=datetime.now() - timedelta(minutes=5),
        total_close_amount=1000.0,
        total_close_volume=0.02,
        avg_close_price=52000.0,
        close_trades_count=1,
        
        # 状态信息
        is_completed=True,
        total_duration_minutes=50.0,
        real_profit=40.0,
        calculated_profit=40.0,
        
        # 交易特征
        is_quick_trade=False,
        is_scalping=False,
        add_position_count=1,
        reduce_position_count=0,
        
        # 风险评估
        risk_score=0.2,
        abnormal_flags=[],
        
        # 其他信息
        leverage=10.0,
        total_fee=2.0,
        
        # 订单类型统计
        market_orders_open=1,
        limit_orders_open=1,
        market_orders_close=0,
        limit_orders_close=1,
        
        # 保证金模式
        cross_margin_positions=1,
        isolated_margin_positions=0
    )
    
    print(f"✅ CompletePosition对象创建成功")
    print(f"   - position_id: {complete_position.position_id}")
    print(f"   - first_open_time: {complete_position.first_open_time}")
    print(f"   - 注意：CompletePosition使用first_open_time，不是open_time")
    
    # 测试转换逻辑（模拟contract_analyzer.py中的转换逻辑）
    try:
        # 模拟_convert_complete_positions_to_position_data方法的核心逻辑
        position_data = PositionData(
            position_id=str(complete_position.position_id),
            member_id=str(complete_position.member_id),
            contract_name=str(complete_position.contract_name),
            primary_side=int(complete_position.primary_side),
            open_time=complete_position.first_open_time,  # 关键：这里正确映射了first_open_time到open_time
            close_time=(complete_position.first_close_time if complete_position.is_completed
                       else complete_position.first_open_time + timedelta(minutes=15) if complete_position.first_open_time
                       else datetime.now()),
            duration_minutes=float(getattr(complete_position, 'total_duration_minutes', 0)),
            total_open_amount=float(complete_position.total_open_amount),
            total_close_amount=float(complete_position.total_close_amount),
            avg_open_price=float(getattr(complete_position, 'avg_open_price', 0)),
            avg_close_price=float(getattr(complete_position, 'avg_close_price', 0)),
            total_pnl=float(getattr(complete_position, 'real_profit', 0)),
            total_commission=float(getattr(complete_position, 'total_fee', 0)),
            net_pnl=float(getattr(complete_position, 'real_profit', 0)) - float(getattr(complete_position, 'total_fee', 0)),
            leverage=float(getattr(complete_position, 'leverage', 1.0)),
            market_orders_open=getattr(complete_position, 'market_orders_open', 0),
            limit_orders_open=getattr(complete_position, 'limit_orders_open', 0),
            market_orders_close=getattr(complete_position, 'market_orders_close', 0),
            limit_orders_close=getattr(complete_position, 'limit_orders_close', 0),
            cross_margin_positions=getattr(complete_position, 'cross_margin_positions', 0),
            isolated_margin_positions=getattr(complete_position, 'isolated_margin_positions', 0)
        )
        
        print(f"✅ PositionData对象转换成功")
        print(f"   - position_id: {position_data.position_id}")
        print(f"   - open_time: {position_data.open_time}")
        print(f"   - 转换成功：first_open_time -> open_time")
        
        # 验证关键属性
        assert hasattr(position_data, 'open_time'), "PositionData对象应该有open_time属性"
        assert position_data.open_time is not None, "open_time不应该为None"
        assert position_data.open_time == complete_position.first_open_time, "open_time应该等于first_open_time"
        
        print(f"✅ 所有验证通过！")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False

def test_error_scenario():
    """测试原始错误场景"""
    print("\n🧪 测试原始错误场景...")
    
    # 创建CompletePosition对象
    complete_position = CompletePosition(
        position_id="error_pos_001",
        member_id="error_user_001",
        contract_name="BTCUSDT",
        first_open_time=datetime.now(),
        last_open_time=datetime.now(),
        total_open_amount=1000.0,
        total_open_volume=0.02,
        avg_open_price=50000.0,
        open_trades_count=1,
        primary_side=1,
        first_close_time=None,
        last_close_time=None,
        total_close_amount=0.0,
        total_close_volume=0.0,
        avg_close_price=0.0,
        close_trades_count=0,
        is_completed=False,
        total_duration_minutes=0.0,
        real_profit=0.0,
        calculated_profit=0.0,
        is_quick_trade=False,
        is_scalping=False,
        add_position_count=0,
        reduce_position_count=0,
        risk_score=0.0,
        abnormal_flags=[]
    )
    
    # 模拟原始错误：直接访问open_time属性
    try:
        # 这会导致AttributeError: 'CompletePosition' object has no attribute 'open_time'
        open_time = complete_position.open_time
        print(f"❌ 意外成功：{open_time}")
        return False
    except AttributeError as e:
        print(f"✅ 成功重现原始错误: {str(e)}")
        print(f"   - CompletePosition对象确实没有open_time属性")
        print(f"   - 它有的是first_open_time属性: {complete_position.first_open_time}")
        return True

if __name__ == "__main__":
    print("🚀 开始测试增量模式修复...")
    
    # 测试转换逻辑
    conversion_success = test_complete_position_to_position_data_conversion()
    
    # 测试错误场景
    error_reproduction_success = test_error_scenario()
    
    print(f"\n📊 测试结果:")
    print(f"   - 转换逻辑测试: {'✅ 通过' if conversion_success else '❌ 失败'}")
    print(f"   - 错误重现测试: {'✅ 通过' if error_reproduction_success else '❌ 失败'}")
    
    if conversion_success and error_reproduction_success:
        print(f"\n🎉 所有测试通过！修复应该有效。")
        print(f"\n💡 修复说明:")
        print(f"   - 问题：增量模式中，_save_position_analysis_data方法接收到CompletePosition对象列表")
        print(f"   - 但该方法期望PositionData对象列表，导致访问open_time属性失败")
        print(f"   - 解决：在增量模式中，先将CompletePosition转换为PositionData再保存")
        print(f"   - 关键：CompletePosition.first_open_time -> PositionData.open_time")
    else:
        print(f"\n❌ 测试失败，需要进一步检查修复逻辑。")
