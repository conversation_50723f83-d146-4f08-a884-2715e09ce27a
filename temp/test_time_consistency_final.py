#!/usr/bin/env python3
"""
最终测试：验证普通模式和增量模式的时间处理一致性
"""

import requests
import json
import time
import pandas as pd
import tempfile
import os

def test_both_modes():
    """测试普通模式和增量模式的时间处理一致性"""
    
    print("最终测试：验证普通模式和增量模式的时间处理一致性")
    print("=" * 70)
    
    # 测试数据 - 使用原始时间格式
    test_data = [
        {
            'positionId': 'pos_final_001',
            'memberId': 'user_final_001',
            'contractName': 'BTCUSDT',
            'side': 1,  # 开多
            'dealVol': 0.01,
            'dealVolUsdt': 1000.0,
            'createTime': '2025/7/24 23:09',  # 原始格式
            'profit': 10.0
        },
        {
            'positionId': 'pos_final_001',
            'memberId': 'user_final_001',
            'contractName': 'BTCUSDT',
            'side': 4,  # 平多
            'dealVol': 0.01,
            'dealVolUsdt': 1000.0,
            'createTime': '2025/7/24 23:15',  # 原始格式
            'profit': -5.0
        }
    ]
    
    # 创建临时CSV文件
    df = pd.DataFrame(test_data)
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
    df.to_csv(temp_file.name, index=False)
    temp_file.close()
    
    print(f"创建临时文件: {temp_file.name}")
    print(f"测试数据:")
    print(df)
    print()
    
    # API调用 - 使用文件上传接口
    url = "http://localhost:5005/api/contract/upload"
    
    results = {}
    
    # 测试两种模式
    for mode in ['normal', 'incremental']:
        print(f"测试{mode}模式...")
        
        try:
            # 准备文件上传
            with open(temp_file.name, 'rb') as f:
                files = {'file': (os.path.basename(temp_file.name), f, 'text/csv')}
                data = {
                    'processing_mode': mode,
                    'async': 'true'
                }
                
                response = requests.post(url, files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ {mode}模式API调用成功!")
                print(f"  状态: {result.get('status', 'unknown')}")
                print(f"  任务ID: {result.get('task_id', 'unknown')}")
                
                # 等待任务完成
                task_id = result.get('task_id')
                if task_id:
                    print(f"  等待任务完成...")
                    for i in range(10):  # 最多等待10次
                        time.sleep(2)
                        status_url = f"http://localhost:5005/api/contract/status/{task_id}"
                        status_response = requests.get(status_url)
                        
                        if status_response.status_code == 200:
                            status_data = status_response.json()
                            task_status = status_data.get('status', 'unknown')
                            
                            if task_status == 'completed':
                                print(f"  ✅ {mode}模式任务完成!")
                                results[mode] = {
                                    'success': True,
                                    'task_id': task_id,
                                    'results': status_data.get('results', [])
                                }
                                break
                            elif task_status == 'failed':
                                print(f"  ❌ {mode}模式任务失败!")
                                error = status_data.get('error', 'unknown error')
                                print(f"    错误信息: {error}")
                                results[mode] = {'success': False, 'error': error}
                                break
                        else:
                            print(f"    状态查询失败: {status_response.status_code}")
                            break
                    else:
                        print(f"  ⏰ {mode}模式任务超时")
                        results[mode] = {'success': False, 'error': 'timeout'}
            else:
                print(f"❌ {mode}模式API调用失败: {response.status_code}")
                print(f"  错误信息: {response.text}")
                results[mode] = {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ {mode}模式API调用异常: {e}")
            results[mode] = {'success': False, 'error': str(e)}
        
        print()
    
    # 分析结果
    print("=" * 70)
    print("测试结果分析:")
    print("=" * 70)
    
    success_count = sum(1 for r in results.values() if r.get('success', False))
    
    if success_count == 2:
        print("🎉 所有测试通过！")
        print("✅ 普通模式和增量模式都能正确处理时间格式")
        print("✅ 时间处理统一化修复成功")
        
        # 检查结果一致性
        normal_results = results.get('normal', {}).get('results', [])
        incremental_results = results.get('incremental', {}).get('results', [])
        
        print(f"\n结果对比:")
        print(f"  普通模式检测结果数量: {len(normal_results)}")
        print(f"  增量模式检测结果数量: {len(incremental_results)}")
        
        if len(normal_results) == len(incremental_results):
            print("✅ 两种模式的检测结果数量一致")
        else:
            print("⚠️  两种模式的检测结果数量不一致（可能由于历史数据差异）")
            
    elif success_count == 1:
        print("⚠️  部分测试通过")
        for mode, result in results.items():
            if result.get('success'):
                print(f"✅ {mode}模式: 成功")
            else:
                print(f"❌ {mode}模式: 失败 - {result.get('error', 'unknown')}")
    else:
        print("❌ 所有测试失败")
        for mode, result in results.items():
            print(f"❌ {mode}模式: {result.get('error', 'unknown')}")
    
    # 清理临时文件
    try:
        os.unlink(temp_file.name)
        print(f"\n清理临时文件: {temp_file.name}")
    except:
        pass
    
    return results

if __name__ == "__main__":
    test_both_modes()
