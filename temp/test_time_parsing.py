#!/usr/bin/env python3
"""
测试时间格式解析
"""

import pandas as pd
from datetime import datetime

def test_time_parsing():
    """测试时间格式解析"""
    
    # 测试数据 - 模拟原始数据格式
    test_times = [
        '2025/7/24 23:09',
        '2025/7/23 21:42:17',
        '2025/07/24 23:09:00',
        '2025-07-24 23:09:00',
        '2025-07-24T23:09:00'
    ]
    
    print("测试时间格式解析:")
    print("=" * 50)
    
    for time_str in test_times:
        print(f"\n原始时间: {time_str}")
        
        try:
            # 使用pandas的智能解析
            parsed_time = pd.to_datetime(time_str, format='mixed', errors='coerce')
            print(f"pandas解析结果: {parsed_time}")
            print(f"类型: {type(parsed_time)}")
            
            # 转换为Python datetime
            if pd.notna(parsed_time):
                py_datetime = parsed_time.to_pydatetime()
                print(f"Python datetime: {py_datetime}")
                print(f"ISO格式: {py_datetime.isoformat()}")
            else:
                print("解析失败")
                
        except Exception as e:
            print(f"解析异常: {e}")
    
    print("\n" + "=" * 50)
    print("测试DataFrame批量解析:")
    
    # 测试DataFrame批量解析
    df = pd.DataFrame({
        'timestamp': test_times,
        'value': [1, 2, 3, 4, 5]
    })
    
    print(f"\n原始DataFrame:")
    print(df)
    print(f"timestamp列类型: {df['timestamp'].dtype}")
    
    # 批量转换
    df['timestamp'] = pd.to_datetime(df['timestamp'], format='mixed', errors='coerce')
    
    print(f"\n转换后DataFrame:")
    print(df)
    print(f"timestamp列类型: {df['timestamp'].dtype}")
    
    # 测试时间差计算
    if len(df) >= 2:
        time_diff = abs((df.iloc[0]['timestamp'] - df.iloc[1]['timestamp']).total_seconds())
        print(f"\n时间差计算测试: {time_diff} 秒")

if __name__ == "__main__":
    test_time_parsing()
