#!/usr/bin/env python3
"""
测试API增量模式调用
"""

import requests
import json
import time
import pandas as pd
import tempfile
import os

def test_api_incremental():
    """测试API增量模式调用"""

    print("测试API增量模式调用")
    print("=" * 50)
    
    # 测试数据 - 使用原始时间格式和正确的字段名
    test_data = [
        {
            'positionId': 'pos_001',  # 使用驼峰命名
            'memberId': 'user_001',   # 使用驼峰命名
            'contractName': 'BTCUSDT', # 使用驼峰命名
            'side': 1,  # 开多
            'dealVol': 0.01,          # 添加交易量字段
            'dealVolUsdt': 1000.0,    # 使用驼峰命名
            'createTime': '2025/7/24 23:09',  # 原始格式
            'profit': 10.0
        },
        {
            'positionId': 'pos_001',
            'memberId': 'user_001',
            'contractName': 'BTCUSDT',
            'side': 4,  # 平多
            'dealVol': 0.01,          # 添加交易量字段
            'dealVolUsdt': 1000.0,
            'createTime': '2025/7/24 23:15',  # 原始格式
            'profit': -5.0
        },
        {
            'positionId': 'pos_002',
            'memberId': 'user_002',
            'contractName': 'BTCUSDT',
            'side': 3,  # 开空
            'dealVol': 0.01,          # 添加交易量字段
            'dealVolUsdt': 1000.0,
            'createTime': '2025/7/24 23:10',  # 原始格式
            'profit': 15.0
        },
        {
            'positionId': 'pos_002',
            'memberId': 'user_002',
            'contractName': 'BTCUSDT',
            'side': 2,  # 平空
            'dealVol': 0.01,          # 添加交易量字段
            'dealVolUsdt': 1000.0,
            'createTime': '2025/7/24 23:16',  # 原始格式
            'profit': -8.0
        }
    ]
    
    # 创建临时CSV文件
    df = pd.DataFrame(test_data)
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
    df.to_csv(temp_file.name, index=False)
    temp_file.close()

    print(f"创建临时文件: {temp_file.name}")
    print(f"数据内容:")
    print(df.head())

    # API调用 - 使用文件上传接口
    url = "http://localhost:5005/api/contract/upload"

    # 测试增量模式
    print("\n测试增量模式...")

    try:
        # 准备文件上传
        with open(temp_file.name, 'rb') as f:
            files = {'file': (os.path.basename(temp_file.name), f, 'text/csv')}
            data = {
                'processing_mode': 'incremental',  # 增量模式
                'async': 'true'  # 异步模式
            }

            response = requests.post(url, files=files, data=data, timeout=30)

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 增量模式API调用成功!")
            print(f"  状态: {result.get('status', 'unknown')}")
            print(f"  任务ID: {result.get('task_id', 'unknown')}")

            # 等待任务完成
            task_id = result.get('task_id')
            if task_id:
                print(f"等待任务完成...")
                for i in range(15):  # 最多等待15次
                    time.sleep(3)
                    status_url = f"http://localhost:5005/api/contract/status/{task_id}"
                    status_response = requests.get(status_url)

                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        task_status = status_data.get('status', 'unknown')
                        progress = status_data.get('progress', {})
                        print(f"  任务状态: {task_status}, 进度: {progress.get('percentage', 0)}%")

                        if task_status == 'completed':
                            print(f"✅ 任务完成!")
                            results = status_data.get('results', [])
                            print(f"  检测结果数量: {len(results)}")
                            break
                        elif task_status == 'failed':
                            print(f"❌ 任务失败!")
                            error = status_data.get('error', 'unknown error')
                            print(f"  错误信息: {error}")
                            break
                    else:
                        print(f"  状态查询失败: {status_response.status_code}")
                        break
        else:
            print(f"❌ 增量模式API调用失败: {response.status_code}")
            print(f"  错误信息: {response.text}")

    except Exception as e:
        print(f"❌ API调用异常: {e}")

    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file.name)
            print(f"\n清理临时文件: {temp_file.name}")
        except:
            pass

if __name__ == "__main__":
    test_api_incremental()
