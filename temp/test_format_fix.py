#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证增量模式格式化修复
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from modules.contract_risk_analysis.services.contract_analyzer import CTContractAnalyzer

def test_format_methods():
    """测试格式化方法的存在性和一致性"""
    print("🧪 测试格式化方法...")
    
    analyzer = CTContractAnalyzer()
    
    # 检查方法是否存在
    has_format_results = hasattr(analyzer, '_format_results')
    has_format_optimized_results = hasattr(analyzer, '_format_optimized_results')
    
    print(f"✅ _format_results 方法存在: {has_format_results}")
    print(f"✅ _format_optimized_results 方法存在: {has_format_optimized_results}")
    
    if not has_format_optimized_results:
        print("❌ _format_optimized_results 方法不存在，这是问题所在！")
        return False
    
    # 创建测试数据
    test_result = {
        'detection_type': 'wash_trading',
        'user_a': {'member_id': 'user_001', 'real_profit': -20.0},
        'user_b': {'member_id': 'user_002', 'real_profit': 20.0},
        'contract_name': 'BTCUSDT',
        'abnormal_volume': 2000.0,
        'reason': '测试对敲',
        'severity': 'High',
        'first_open_time': None,
        'wash_score': 0.8,
        'profit_hedge_score': 1.0,
        'total_profit': 0.0,
        'detection_method': 'cross_account_wash_trading',
        'trade_pair_detail': {
            'user_a': {'member_id': 'user_001'},
            'user_b': {'member_id': 'user_002'},
            'time_gaps': {}
        }
    }
    
    try:
        # 测试 _format_optimized_results
        formatted_optimized = analyzer._format_optimized_results([test_result])
        print(f"✅ _format_optimized_results 执行成功，结果数: {len(formatted_optimized)}")
        
        if formatted_optimized:
            result = formatted_optimized[0]
            print(f"📊 _format_optimized_results 结果格式:")
            print(f"   - detection_type: {result.get('detection_type')}")
            print(f"   - memberId: {result.get('memberId')}")
            print(f"   - opponent_member_id: {result.get('opponent_member_id')}")
            print(f"   - trade_pair_detail: {'✅ 存在' if result.get('trade_pair_detail') else '❌ 缺失'}")
            print(f"   - wash_score: {result.get('wash_score')}")
            print(f"   - profit_hedge_score: {result.get('profit_hedge_score')}")
        
        # 测试 _format_results
        formatted_basic = analyzer._format_results([test_result])
        print(f"✅ _format_results 执行成功，结果数: {len(formatted_basic)}")
        
        if formatted_basic:
            result = formatted_basic[0]
            print(f"📊 _format_results 结果格式:")
            print(f"   - detection_type: {result.get('detection_type')}")
            print(f"   - memberId: {result.get('memberId')}")
            print(f"   - opponent_member_id: {result.get('opponent_member_id')}")
            print(f"   - trade_pair_detail: {'✅ 存在' if result.get('trade_pair_detail') else '❌ 缺失'}")
            print(f"   - wash_score: {result.get('wash_score')}")
            print(f"   - profit_hedge_score: {result.get('profit_hedge_score')}")
        
        # 比较两种格式化方法的差异
        print(f"\n🔍 比较两种格式化方法:")
        optimized_keys = set(formatted_optimized[0].keys()) if formatted_optimized else set()
        basic_keys = set(formatted_basic[0].keys()) if formatted_basic else set()
        
        only_in_optimized = optimized_keys - basic_keys
        only_in_basic = basic_keys - optimized_keys
        common_keys = optimized_keys & basic_keys
        
        print(f"   - 共同字段: {len(common_keys)} 个")
        print(f"   - 仅在 _format_optimized_results: {len(only_in_optimized)} 个")
        if only_in_optimized:
            print(f"     * {list(only_in_optimized)}")
        print(f"   - 仅在 _format_results: {len(only_in_basic)} 个")
        if only_in_basic:
            print(f"     * {list(only_in_basic)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 格式化方法测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_incremental_mode_fix():
    """测试增量模式修复"""
    print(f"\n🧪 验证增量模式修复...")
    
    # 检查源代码中的修复
    analyzer_file = os.path.join(os.path.dirname(__file__), '..', 'backend', 
                                'modules', 'contract_risk_analysis', 'services', 'contract_analyzer.py')
    
    try:
        with open(analyzer_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含修复的代码
        if '_format_optimized_results(all_results)' in content:
            print("✅ 发现修复代码：增量模式使用 _format_optimized_results")
            
            # 检查是否在正确的位置
            if '# 🚀 修复：使用与普通模式相同的格式化方法' in content:
                print("✅ 修复注释存在，确认修复已正确应用")
                return True
            else:
                print("⚠️  修复代码存在但缺少注释")
                return True
        else:
            print("❌ 未发现修复代码，增量模式可能仍使用旧的格式化方法")
            return False
            
    except Exception as e:
        print(f"❌ 无法检查源代码: {e}")
        return False

def main():
    print("🚀 开始验证增量模式格式化修复...")
    
    # 测试格式化方法
    format_test_passed = test_format_methods()
    
    # 验证修复
    fix_verified = test_incremental_mode_fix()
    
    print(f"\n📊 验证结果:")
    print(f"   - 格式化方法测试: {'✅ 通过' if format_test_passed else '❌ 失败'}")
    print(f"   - 修复验证: {'✅ 通过' if fix_verified else '❌ 失败'}")
    
    if format_test_passed and fix_verified:
        print(f"\n🎉 修复验证成功！")
        print(f"💡 关键改进:")
        print(f"   - 增量模式现在使用 _format_optimized_results 方法")
        print(f"   - 与普通模式使用相同的格式化逻辑")
        print(f"   - 确保返回的数据结构包含所有必需字段")
        print(f"   - trade_pair_detail、opponent_member_id 等字段将正确包含")
        
        print(f"\n📋 预期效果:")
        print(f"   - 增量模式返回的数据将包含 opponent_member_id")
        print(f"   - 增量模式返回的数据将包含 trade_pair_detail")
        print(f"   - 增量模式返回的数据将包含 wash_score、profit_hedge_score")
        print(f"   - 增量模式返回的数据将包含 reason 字段")
        print(f"   - 前端显示将与普通模式保持一致")
    else:
        print(f"\n❌ 修复验证失败，需要进一步检查。")

if __name__ == "__main__":
    main()
