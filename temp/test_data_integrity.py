#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据完整性和格式验证测试
验证CompletePosition到PositionData转换的数据完整性
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import pandas as pd
from datetime import datetime, timedelta
from modules.contract_risk_analysis.optimizers.position_based_optimizer import CompletePosition
from modules.user_analysis.models.user_behavior_models import PositionData

def create_test_complete_position():
    """创建一个完整的测试CompletePosition对象"""
    return CompletePosition(
        # 基本信息
        position_id="test_pos_001",
        member_id="test_user_001", 
        contract_name="BTCUSDT",
        
        # 开仓信息聚合
        first_open_time=datetime(2025, 8, 3, 10, 0, 0),
        last_open_time=datetime(2025, 8, 3, 10, 5, 0),
        total_open_amount=1000.0,
        total_open_volume=0.02,
        avg_open_price=50000.0,
        open_trades_count=2,
        primary_side=1,
        
        # 平仓信息聚合
        first_close_time=datetime(2025, 8, 3, 11, 0, 0),
        last_close_time=datetime(2025, 8, 3, 11, 5, 0),
        total_close_amount=1040.0,
        total_close_volume=0.02,
        avg_close_price=52000.0,
        close_trades_count=1,
        
        # 订单特征
        is_completed=True,
        total_duration_minutes=65.0,
        real_profit=40.0,
        calculated_profit=40.0,
        
        # 交易行为特征
        is_quick_trade=False,
        is_scalping=False,
        add_position_count=1,
        reduce_position_count=0,
        
        # 风险指标
        risk_score=0.2,
        abnormal_flags=["high_frequency"],
        
        # 杠杆和手续费信息
        leverage=10.0,
        total_fee=2.5,
        
        # 订单类型统计
        market_orders_open=1,
        limit_orders_open=1,
        market_orders_close=0,
        limit_orders_close=1,
        
        # 仓位模式标识
        cross_margin_positions=1,
        isolated_margin_positions=0
    )

def simulate_conversion_logic(complete_position, task_id="test_task"):
    """模拟contract_analyzer.py中的转换逻辑"""
    
    # 安全转换函数（复制自contract_analyzer.py）
    def safe_datetime(dt_value):
        if dt_value is None:
            return datetime.now()
        if isinstance(dt_value, str):
            try:
                return pd.to_datetime(dt_value)
            except:
                return datetime.now()
        return dt_value

    def safe_float(value, default=0.0):
        if value is None:
            return default
        try:
            if hasattr(value, 'item'):
                return float(value.item())
            return float(value)
        except (ValueError, TypeError):
            return default

    def safe_int(value, default=0):
        if value is None:
            return default
        try:
            if hasattr(value, 'item'):
                return int(value.item())
            return int(value)
        except (ValueError, TypeError):
            return default

    def safe_str(value, default=""):
        if value is None:
            return default
        return str(value)

    # 计算手续费
    total_commission = safe_float(getattr(complete_position, 'total_fee', 0.0))
    
    # 计算净盈利（盈利减去手续费）
    real_profit = safe_float(getattr(complete_position, 'real_profit', 0.0))
    net_pnl = real_profit - total_commission

    # 执行转换（复制自contract_analyzer.py的转换逻辑）
    position_data = PositionData(
        position_id=safe_str(complete_position.position_id),
        member_id=safe_str(complete_position.member_id),
        contract_name=safe_str(complete_position.contract_name),
        primary_side=safe_int(complete_position.primary_side, 1),
        open_time=safe_datetime(complete_position.first_open_time),
        close_time=(safe_datetime(complete_position.first_close_time) if complete_position.is_completed
                   else safe_datetime(complete_position.first_open_time) + timedelta(minutes=15) if complete_position.first_open_time
                   else datetime.now()),
        duration_minutes=safe_float(getattr(complete_position, 'total_duration_minutes', 0)),
        total_open_amount=safe_float(complete_position.total_open_amount),
        total_close_amount=safe_float(complete_position.total_close_amount),
        avg_open_price=safe_float(getattr(complete_position, 'avg_open_price', 0)),
        avg_close_price=safe_float(getattr(complete_position, 'avg_close_price', 0)),
        total_pnl=real_profit,
        total_commission=total_commission,
        net_pnl=net_pnl,
        leverage=safe_float(getattr(complete_position, 'leverage', 1.0), 1.0),
        task_id=safe_str(task_id),
        # 订单类型统计
        market_orders_open=getattr(complete_position, 'market_orders_open', 0),
        limit_orders_open=getattr(complete_position, 'limit_orders_open', 0),
        market_orders_close=getattr(complete_position, 'market_orders_close', 0),
        limit_orders_close=getattr(complete_position, 'limit_orders_close', 0),
        # 仓位模式统计
        cross_margin_positions=getattr(complete_position, 'cross_margin_positions', 0),
        isolated_margin_positions=getattr(complete_position, 'isolated_margin_positions', 0)
    )
    
    return position_data

def test_data_integrity():
    """测试数据完整性"""
    print("🧪 测试数据完整性...")
    
    # 创建测试数据
    complete_position = create_test_complete_position()
    
    # 执行转换
    position_data = simulate_conversion_logic(complete_position)
    
    # 验证基本信息
    integrity_issues = []
    
    # 1. 基本字段映射验证
    basic_mappings = [
        ('position_id', 'position_id'),
        ('member_id', 'member_id'),
        ('contract_name', 'contract_name'),
        ('primary_side', 'primary_side'),
    ]
    
    for cp_field, pd_field in basic_mappings:
        cp_value = getattr(complete_position, cp_field)
        pd_value = getattr(position_data, pd_field)
        if str(cp_value) != str(pd_value):
            integrity_issues.append(f"基本字段映射错误: {cp_field}({cp_value}) -> {pd_field}({pd_value})")
    
    # 2. 时间字段映射验证
    if complete_position.first_open_time != position_data.open_time:
        integrity_issues.append(f"时间映射错误: first_open_time({complete_position.first_open_time}) -> open_time({position_data.open_time})")
    
    if complete_position.first_close_time != position_data.close_time:
        integrity_issues.append(f"时间映射错误: first_close_time({complete_position.first_close_time}) -> close_time({position_data.close_time})")
    
    # 3. 数值字段映射验证
    numeric_mappings = [
        ('total_open_amount', 'total_open_amount'),
        ('total_close_amount', 'total_close_amount'),
        ('avg_open_price', 'avg_open_price'),
        ('avg_close_price', 'avg_close_price'),
        ('total_duration_minutes', 'duration_minutes'),
        ('leverage', 'leverage'),
    ]
    
    for cp_field, pd_field in numeric_mappings:
        cp_value = getattr(complete_position, cp_field)
        pd_value = getattr(position_data, pd_field)
        if abs(float(cp_value) - float(pd_value)) > 0.001:  # 允许浮点误差
            integrity_issues.append(f"数值字段映射错误: {cp_field}({cp_value}) -> {pd_field}({pd_value})")
    
    # 4. 盈亏计算验证
    expected_net_pnl = complete_position.real_profit - complete_position.total_fee
    if abs(position_data.net_pnl - expected_net_pnl) > 0.001:
        integrity_issues.append(f"净盈亏计算错误: 期望{expected_net_pnl}, 实际{position_data.net_pnl}")
    
    # 5. 订单类型统计验证
    order_type_mappings = [
        ('market_orders_open', 'market_orders_open'),
        ('limit_orders_open', 'limit_orders_open'),
        ('market_orders_close', 'market_orders_close'),
        ('limit_orders_close', 'limit_orders_close'),
    ]
    
    for cp_field, pd_field in order_type_mappings:
        cp_value = getattr(complete_position, cp_field)
        pd_value = getattr(position_data, pd_field)
        if cp_value != pd_value:
            integrity_issues.append(f"订单类型统计错误: {cp_field}({cp_value}) -> {pd_field}({pd_value})")
    
    # 6. 仓位模式验证
    margin_mappings = [
        ('cross_margin_positions', 'cross_margin_positions'),
        ('isolated_margin_positions', 'isolated_margin_positions'),
    ]
    
    for cp_field, pd_field in margin_mappings:
        cp_value = getattr(complete_position, cp_field)
        pd_value = getattr(position_data, pd_field)
        if cp_value != pd_value:
            integrity_issues.append(f"仓位模式统计错误: {cp_field}({cp_value}) -> {pd_field}({pd_value})")
    
    # 输出结果
    if integrity_issues:
        print("❌ 发现数据完整性问题:")
        for issue in integrity_issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 数据完整性验证通过")
        return True

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    edge_cases_passed = 0
    total_edge_cases = 0
    
    # 测试1: None值处理
    total_edge_cases += 1
    try:
        incomplete_position = CompletePosition(
            position_id="incomplete_001",
            member_id="user_001",
            contract_name="BTCUSDT",
            first_open_time=datetime.now(),
            last_open_time=datetime.now(),
            total_open_amount=1000.0,
            total_open_volume=0.02,
            avg_open_price=50000.0,
            open_trades_count=1,
            primary_side=1,
            first_close_time=None,  # None值
            last_close_time=None,   # None值
            total_close_amount=0.0,
            total_close_volume=0.0,
            avg_close_price=0.0,
            close_trades_count=0,
            is_completed=False,
            total_duration_minutes=0.0,
            real_profit=0.0,
            calculated_profit=0.0,
            is_quick_trade=False,
            is_scalping=False,
            add_position_count=0,
            reduce_position_count=0,
            risk_score=0.0,
            abnormal_flags=[]
        )
        
        position_data = simulate_conversion_logic(incomplete_position)
        print("✅ None值处理测试通过")
        edge_cases_passed += 1
    except Exception as e:
        print(f"❌ None值处理测试失败: {e}")
    
    # 测试2: 极值处理
    total_edge_cases += 1
    try:
        extreme_position = create_test_complete_position()
        extreme_position.leverage = 999.99
        extreme_position.total_fee = 0.0001
        extreme_position.real_profit = -999999.99
        
        position_data = simulate_conversion_logic(extreme_position)
        print("✅ 极值处理测试通过")
        edge_cases_passed += 1
    except Exception as e:
        print(f"❌ 极值处理测试失败: {e}")
    
    return edge_cases_passed == total_edge_cases

def test_end_to_end_data_flow():
    """测试端到端数据流：DataFrame -> CompletePosition -> PositionData"""
    print("\n🧪 测试端到端数据流...")

    # 1. 创建模拟DataFrame（增量模式的输入数据）
    test_data = {
        'position_id': ['pos_001'],
        'member_id': ['user_001'],
        'contract_name': ['BTCUSDT'],
        'first_open_time': [datetime(2025, 8, 3, 10, 0, 0)],
        'last_open_time': [datetime(2025, 8, 3, 10, 5, 0)],
        'total_open_amount': [1000.0],
        'total_open_volume': [0.02],
        'avg_open_price': [50000.0],
        'open_trades_count': [2],
        'primary_side': [1],
        'first_close_time': [datetime(2025, 8, 3, 11, 0, 0)],
        'last_close_time': [datetime(2025, 8, 3, 11, 5, 0)],
        'total_close_amount': [1040.0],
        'total_close_volume': [0.02],
        'avg_close_price': [52000.0],
        'close_trades_count': [1],
        'is_completed': [True],
        'total_duration_minutes': [65.0],
        'real_profit': [40.0],
        'calculated_profit': [40.0],
        'is_quick_trade': [False],
        'is_scalping': [False],
        'add_position_count': [1],
        'reduce_position_count': [0],
        'risk_score': [0.2],
        'abnormal_flags': [[]],
        'leverage': [10.0],
        'total_fee': [2.5],
        'market_orders_open': [1],
        'limit_orders_open': [1],
        'market_orders_close': [0],
        'limit_orders_close': [1],
        'cross_margin_positions': [1],
        'isolated_margin_positions': [0]
    }

    df = pd.DataFrame(test_data)

    # 2. 模拟DataFrame到CompletePosition的转换
    try:
        row = df.iloc[0]
        complete_position = CompletePosition(
            position_id=str(row['position_id']),
            member_id=str(row['member_id']),
            contract_name=str(row['contract_name']),
            first_open_time=row.get('first_open_time'),
            last_open_time=row.get('last_open_time'),
            total_open_amount=float(row.get('total_open_amount', 0.0)),
            total_open_volume=float(row.get('total_open_volume', 0.0)),
            avg_open_price=float(row.get('avg_open_price', 0.0)),
            open_trades_count=int(row.get('open_trades_count', 0)),
            primary_side=int(row.get('primary_side', 1)),
            first_close_time=row.get('first_close_time'),
            last_close_time=row.get('last_close_time'),
            total_close_amount=float(row.get('total_close_amount', 0.0)),
            total_close_volume=float(row.get('total_close_volume', 0.0)),
            avg_close_price=float(row.get('avg_close_price', 0.0)),
            close_trades_count=int(row.get('close_trades_count', 0)),
            is_completed=bool(row.get('is_completed', False)),
            total_duration_minutes=float(row.get('total_duration_minutes', 0.0)),
            real_profit=float(row.get('real_profit', 0.0)),
            calculated_profit=float(row.get('calculated_profit', 0.0)),
            is_quick_trade=bool(row.get('is_quick_trade', False)),
            is_scalping=bool(row.get('is_scalping', False)),
            add_position_count=int(row.get('add_position_count', 0)),
            reduce_position_count=int(row.get('reduce_position_count', 0)),
            risk_score=float(row.get('risk_score', 0.0)),
            abnormal_flags=row.get('abnormal_flags', []) if isinstance(row.get('abnormal_flags'), list) else [],
            leverage=float(row.get('leverage', 1.0)),
            total_fee=float(row.get('total_fee', 0.0)),
            market_orders_open=int(row.get('market_orders_open', 0)),
            limit_orders_open=int(row.get('limit_orders_open', 0)),
            market_orders_close=int(row.get('market_orders_close', 0)),
            limit_orders_close=int(row.get('limit_orders_close', 0)),
            cross_margin_positions=int(row.get('cross_margin_positions', 0)),
            isolated_margin_positions=int(row.get('isolated_margin_positions', 0))
        )

        print("✅ DataFrame -> CompletePosition 转换成功")

        # 3. CompletePosition到PositionData的转换
        position_data = simulate_conversion_logic(complete_position)
        print("✅ CompletePosition -> PositionData 转换成功")

        # 4. 验证端到端数据一致性
        original_data = df.iloc[0]

        # 验证关键数据点
        checks = [
            (original_data['position_id'], position_data.position_id, "position_id"),
            (original_data['member_id'], position_data.member_id, "member_id"),
            (original_data['first_open_time'], position_data.open_time, "open_time"),
            (original_data['total_open_amount'], position_data.total_open_amount, "total_open_amount"),
            (original_data['real_profit'], position_data.total_pnl, "total_pnl"),
            (original_data['total_fee'], position_data.total_commission, "total_commission"),
            (original_data['leverage'], position_data.leverage, "leverage"),
        ]

        all_checks_passed = True
        for original, converted, field_name in checks:
            if str(original) != str(converted):
                print(f"❌ 端到端数据不一致: {field_name} 原始({original}) != 转换后({converted})")
                all_checks_passed = False

        if all_checks_passed:
            print("✅ 端到端数据一致性验证通过")
            return True
        else:
            return False

    except Exception as e:
        print(f"❌ 端到端测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("🚀 开始数据完整性和格式验证测试...")

    # 测试数据完整性
    integrity_passed = test_data_integrity()

    # 测试边界情况
    edge_cases_passed = test_edge_cases()

    # 测试端到端数据流
    end_to_end_passed = test_end_to_end_data_flow()

    print(f"\n📊 测试结果:")
    print(f"   - 数据完整性测试: {'✅ 通过' if integrity_passed else '❌ 失败'}")
    print(f"   - 边界情况测试: {'✅ 通过' if edge_cases_passed else '❌ 失败'}")
    print(f"   - 端到端数据流测试: {'✅ 通过' if end_to_end_passed else '❌ 失败'}")

    if integrity_passed and edge_cases_passed and end_to_end_passed:
        print(f"\n🎉 所有测试通过！数据转换逻辑完整且可靠。")
        print(f"\n📋 验证的数据流:")
        print(f"   1. DataFrame (增量模式输入)")
        print(f"   2. -> CompletePosition (中间对象)")
        print(f"   3. -> PositionData (最终保存对象)")
        print(f"   4. -> 数据库 (position_analysis表)")
        print(f"\n🔍 验证的数据映射:")
        print(f"   - 基本信息: position_id, member_id, contract_name, primary_side")
        print(f"   - 时间信息: first_open_time -> open_time, first_close_time -> close_time")
        print(f"   - 金额信息: total_open_amount, total_close_amount, avg_open_price, avg_close_price")
        print(f"   - 盈亏信息: real_profit -> total_pnl, total_fee -> total_commission, 计算net_pnl")
        print(f"   - 订单统计: market_orders_*, limit_orders_*")
        print(f"   - 仓位模式: cross_margin_positions, isolated_margin_positions")
        print(f"   - 其他信息: leverage, duration_minutes, task_id")
    else:
        print(f"\n❌ 测试失败，需要检查数据转换逻辑。")
