#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON序列化修复
验证PositionData对象能否正确序列化
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import json
from datetime import datetime
from modules.user_analysis.models.user_behavior_models import PositionData
from database.algorithm_storage_manager import json_serializable

def test_position_data_serialization():
    """测试PositionData对象的JSON序列化"""
    print("🧪 测试PositionData对象的JSON序列化...")
    
    # 创建一个PositionData对象
    position = PositionData(
        position_id="test_pos_001",
        member_id="test_user_001",
        contract_name="BTCUSDT",
        primary_side=1,
        open_time=datetime.now(),
        close_time=datetime.now(),
        duration_minutes=30.5,
        total_open_amount=1000.0,
        total_close_amount=1050.0,
        avg_open_price=50000.0,
        avg_close_price=52500.0,
        total_pnl=50.0,
        total_commission=2.5,
        net_pnl=47.5,
        leverage=10.0,
        market_orders_open=1,
        limit_orders_open=0,
        market_orders_close=0,
        limit_orders_close=1,
        cross_margin_positions=1,
        isolated_margin_positions=0
    )
    
    print(f"原始PositionData对象: {position}")
    
    # 测试直接JSON序列化（应该失败）
    try:
        direct_json = json.dumps(position)
        print("❌ 直接JSON序列化不应该成功")
    except Exception as e:
        print(f"✅ 直接JSON序列化失败（预期）: {e}")
    
    # 测试使用json_serializable函数
    try:
        serializable_data = json_serializable(position)
        json_str = json.dumps(serializable_data)
        print(f"✅ 使用json_serializable成功: {len(json_str)} 字符")
        print(f"序列化结果: {json_str[:200]}...")
        
        # 测试反序列化
        deserialized = json.loads(json_str)
        print(f"✅ 反序列化成功: {type(deserialized)}")
        print(f"反序列化数据: {deserialized}")
        
    except Exception as e:
        print(f"❌ 使用json_serializable失败: {e}")
        return False
    
    return True

def test_complex_data_structure():
    """测试包含PositionData的复杂数据结构"""
    print("\n🧪 测试包含PositionData的复杂数据结构...")
    
    position1 = PositionData(
        position_id="pos_001",
        member_id="user_001",
        contract_name="BTCUSDT",
        primary_side=1,
        open_time=datetime.now(),
        close_time=None,
        duration_minutes=15.0,
        total_open_amount=500.0,
        total_close_amount=0.0,
        avg_open_price=50000.0,
        avg_close_price=0.0,
        total_pnl=0.0,
        total_commission=1.0,
        net_pnl=-1.0,
        leverage=5.0
    )
    
    position2 = PositionData(
        position_id="pos_002",
        member_id="user_002",
        contract_name="ETHUSDT",
        primary_side=3,
        open_time=datetime.now(),
        close_time=datetime.now(),
        duration_minutes=45.0,
        total_open_amount=800.0,
        total_close_amount=820.0,
        avg_open_price=3000.0,
        avg_close_price=2950.0,
        total_pnl=20.0,
        total_commission=1.5,
        net_pnl=18.5,
        leverage=20.0
    )
    
    # 创建包含PositionData的复杂数据结构
    complex_data = {
        'risk_type': 'wash_trading',
        'detection_method': 'position_based',
        'positions': [position1, position2],
        'metadata': {
            'analysis_time': datetime.now(),
            'total_positions': 2,
            'risk_score': 0.85,
            'position_details': {
                'pos_001': position1,
                'pos_002': position2
            }
        },
        'counterparty_ids': ['user_003', 'user_004'],
        'additional_info': {
            'nested_position': position1,
            'risk_factors': ['high_frequency', 'opposite_sides']
        }
    }
    
    try:
        serializable_data = json_serializable(complex_data)
        json_str = json.dumps(serializable_data, ensure_ascii=False)
        print(f"✅ 复杂数据结构序列化成功: {len(json_str)} 字符")
        
        # 验证反序列化
        deserialized = json.loads(json_str)
        print(f"✅ 复杂数据结构反序列化成功")
        print(f"包含positions数量: {len(deserialized.get('positions', []))}")
        print(f"包含metadata: {bool(deserialized.get('metadata'))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 复杂数据结构序列化失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试JSON序列化修复...")
    
    success1 = test_position_data_serialization()
    success2 = test_complex_data_structure()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！JSON序列化修复成功。")
    else:
        print("\n❌ 测试失败，需要进一步检查。")
