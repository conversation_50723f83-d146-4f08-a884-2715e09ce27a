#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增量模式和普通模式的数据格式一致性
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import pandas as pd
from datetime import datetime, timedelta
from modules.contract_risk_analysis.services.contract_analyzer import CTContractAnalyzer
from modules.contract_risk_analysis.optimizers.position_based_optimizer import PositionBasedOptimizer, CompletePosition

def create_test_data():
    """创建测试数据"""
    base_time = datetime(2025, 8, 3, 10, 0, 0)
    
    # 创建对敲测试数据
    test_data = [
        {
            'position_id': 'pos_001',
            'member_id': 'user_001',
            'contract_name': 'BTCUSDT',
            'side': 1,  # 开多
            'order_create_time': base_time,
            'deal_vol_usdt': 1000.0,
            'profit': -20.0
        },
        {
            'position_id': 'pos_001',
            'member_id': 'user_001',
            'contract_name': 'BTCUSDT',
            'side': 4,  # 平多
            'order_create_time': base_time + timedelta(minutes=5),
            'deal_vol_usdt': 1000.0,
            'profit': -20.0
        },
        {
            'position_id': 'pos_002',
            'member_id': 'user_002',
            'contract_name': 'BTCUSDT',
            'side': 3,  # 开空
            'order_create_time': base_time + timedelta(seconds=10),
            'deal_vol_usdt': 1000.0,
            'profit': 20.0
        },
        {
            'position_id': 'pos_002',
            'member_id': 'user_002',
            'contract_name': 'BTCUSDT',
            'side': 2,  # 平空
            'order_create_time': base_time + timedelta(minutes=5, seconds=10),
            'deal_vol_usdt': 1000.0,
            'profit': 20.0
        }
    ]
    
    return pd.DataFrame(test_data)

def create_complete_positions():
    """创建CompletePosition对象用于增量模式测试"""
    base_time = datetime(2025, 8, 3, 10, 0, 0)
    
    position_a = CompletePosition(
        position_id="pos_001",
        member_id="user_001",
        contract_name="BTCUSDT",
        
        # 开仓信息
        first_open_time=base_time,
        last_open_time=base_time,
        total_open_amount=1000.0,
        total_open_volume=0.02,
        avg_open_price=50000.0,
        open_trades_count=1,
        primary_side=1,  # 开多
        
        # 平仓信息
        first_close_time=base_time + timedelta(minutes=5),
        last_close_time=base_time + timedelta(minutes=5),
        total_close_amount=1000.0,
        total_close_volume=0.02,
        avg_close_price=49000.0,
        close_trades_count=1,
        
        # 状态信息
        is_completed=True,
        total_duration_minutes=5.0,
        real_profit=-20.0,
        calculated_profit=-20.0,
        
        # 其他信息
        is_quick_trade=True,
        is_scalping=False,
        add_position_count=0,
        reduce_position_count=0,
        risk_score=0.8,
        abnormal_flags=[],
        leverage=10.0,
        total_fee=2.0
    )
    
    position_b = CompletePosition(
        position_id="pos_002",
        member_id="user_002",
        contract_name="BTCUSDT",
        
        # 开仓信息
        first_open_time=base_time + timedelta(seconds=10),
        last_open_time=base_time + timedelta(seconds=10),
        total_open_amount=1000.0,
        total_open_volume=0.02,
        avg_open_price=50000.0,
        open_trades_count=1,
        primary_side=3,  # 开空
        
        # 平仓信息
        first_close_time=base_time + timedelta(minutes=5, seconds=10),
        last_close_time=base_time + timedelta(minutes=5, seconds=10),
        total_close_amount=1000.0,
        total_close_volume=0.02,
        avg_close_price=51000.0,
        close_trades_count=1,
        
        # 状态信息
        is_completed=True,
        total_duration_minutes=5.0,
        real_profit=20.0,
        calculated_profit=20.0,
        
        # 其他信息
        is_quick_trade=True,
        is_scalping=False,
        add_position_count=0,
        reduce_position_count=0,
        risk_score=0.8,
        abnormal_flags=[],
        leverage=10.0,
        total_fee=2.0
    )
    
    return {"pos_001": position_a, "pos_002": position_b}

def test_normal_mode():
    """测试普通模式的数据格式"""
    print("🧪 测试普通模式数据格式...")
    
    try:
        # 创建测试数据
        df = create_test_data()
        
        # 创建分析器
        analyzer = CTContractAnalyzer()
        
        # 执行普通模式分析
        results = analyzer.process_contract_data(df, is_pre_processed=False)
        
        print(f"✅ 普通模式分析完成，结果数: {len(results)}")
        
        if results:
            result = results[0]
            print(f"📊 普通模式结果格式:")
            print(f"   - detection_type: {result.get('detection_type')}")
            print(f"   - memberId: {result.get('memberId')}")
            print(f"   - contractName: {result.get('contractName')}")
            print(f"   - opponent_member_id: {result.get('opponent_member_id')}")
            print(f"   - trade_pair_detail: {'✅ 存在' if result.get('trade_pair_detail') else '❌ 缺失'}")
            print(f"   - wash_score: {result.get('wash_score')}")
            print(f"   - profit_hedge_score: {result.get('profit_hedge_score')}")
            print(f"   - reason: {result.get('reason')}")
            print(f"   - timeRange: {result.get('timeRange')}")
            
            return result
        else:
            print("❌ 普通模式没有检测到结果")
            return None
            
    except Exception as e:
        print(f"❌ 普通模式测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def test_incremental_mode():
    """测试增量模式的数据格式"""
    print("\n🧪 测试增量模式数据格式...")
    
    try:
        # 创建完整订单数据
        complete_positions = create_complete_positions()
        
        # 创建分析器
        analyzer = CTContractAnalyzer()
        
        # 模拟增量模式的数据处理
        # 直接设置完整订单到优化器中
        analyzer.position_optimizer = PositionBasedOptimizer()
        analyzer.position_optimizer.complete_positions = complete_positions

        # 构建索引
        for position in complete_positions.values():
            analyzer.position_optimizer._build_indexes(position)

        # 创建空的DataFrame（增量模式不需要原始数据）
        df = pd.DataFrame()

        # 执行增量模式分析（预处理模式）
        results = analyzer._process_pre_built_positions(df)
        
        print(f"✅ 增量模式分析完成，结果数: {len(results)}")
        
        if results:
            result = results[0]
            print(f"📊 增量模式结果格式:")
            print(f"   - detection_type: {result.get('detection_type')}")
            print(f"   - memberId: {result.get('memberId')}")
            print(f"   - contractName: {result.get('contractName')}")
            print(f"   - opponent_member_id: {result.get('opponent_member_id')}")
            print(f"   - trade_pair_detail: {'✅ 存在' if result.get('trade_pair_detail') else '❌ 缺失'}")
            print(f"   - wash_score: {result.get('wash_score')}")
            print(f"   - profit_hedge_score: {result.get('profit_hedge_score')}")
            print(f"   - reason: {result.get('reason')}")
            print(f"   - timeRange: {result.get('timeRange')}")
            
            return result
        else:
            print("❌ 增量模式没有检测到结果")
            return None
            
    except Exception as e:
        print(f"❌ 增量模式测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def compare_results(normal_result, incremental_result):
    """比较两种模式的结果格式"""
    print("\n🔍 比较两种模式的结果格式...")
    
    if not normal_result or not incremental_result:
        print("❌ 无法比较，某个模式没有返回结果")
        return False
    
    # 关键字段列表
    key_fields = [
        'detection_type', 'memberId', 'contractName', 'opponent_member_id',
        'trade_pair_detail', 'wash_score', 'profit_hedge_score', 'reason', 'timeRange'
    ]
    
    differences = []
    similarities = []
    
    for field in key_fields:
        normal_value = normal_result.get(field)
        incremental_value = incremental_result.get(field)
        
        if field == 'trade_pair_detail':
            # 特殊处理trade_pair_detail
            normal_has = normal_value is not None
            incremental_has = incremental_value is not None
            if normal_has == incremental_has:
                similarities.append(f"{field}: 都{'有' if normal_has else '无'}")
            else:
                differences.append(f"{field}: 普通模式{'有' if normal_has else '无'}, 增量模式{'有' if incremental_has else '无'}")
        else:
            if normal_value == incremental_value:
                similarities.append(f"{field}: {normal_value}")
            else:
                differences.append(f"{field}: 普通模式={normal_value}, 增量模式={incremental_value}")
    
    print(f"✅ 相同字段 ({len(similarities)}):")
    for similarity in similarities:
        print(f"   - {similarity}")
    
    if differences:
        print(f"❌ 不同字段 ({len(differences)}):")
        for difference in differences:
            print(f"   - {difference}")
        return False
    else:
        print(f"🎉 所有关键字段都一致！")
        return True

if __name__ == "__main__":
    print("🚀 开始数据格式一致性测试...")
    
    # 测试普通模式
    normal_result = test_normal_mode()
    
    # 测试增量模式
    incremental_result = test_incremental_mode()
    
    # 比较结果
    is_consistent = compare_results(normal_result, incremental_result)
    
    print(f"\n📊 测试结果:")
    if is_consistent:
        print(f"🎉 数据格式一致性测试通过！")
        print(f"   - 普通模式和增量模式返回相同的数据结构")
        print(f"   - 所有关键字段都存在且格式一致")
        print(f"   - trade_pair_detail 详细信息完整")
    else:
        print(f"❌ 数据格式一致性测试失败！")
        print(f"   - 普通模式和增量模式返回的数据结构不一致")
        print(f"   - 需要检查格式化方法的实现")
