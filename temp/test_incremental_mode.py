#!/usr/bin/env python3
"""
测试增量模式时间处理
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import pandas as pd
from datetime import datetime
from modules.contract_risk_analysis.optimizers.position_based_optimizer import PositionBasedOptimizer

def test_incremental_mode():
    """测试增量模式时间处理"""
    
    print("测试增量模式时间处理")
    print("=" * 50)
    
    # 创建测试数据 - 模拟原始时间格式
    test_data = [
        {
            'position_id': 'pos_001',
            'member_id': 'user_001',
            'contract_name': 'BTCUSDT',
            'side': 1,  # 开多
            'deal_vol_usdt': 1000.0,
            'timestamp': '2025/7/24 23:09',  # 原始格式
            'profit': 10.0
        },
        {
            'position_id': 'pos_001',
            'member_id': 'user_001',
            'contract_name': 'BTCUSDT',
            'side': 4,  # 平多
            'deal_vol_usdt': 1000.0,
            'timestamp': '2025/7/24 23:15',  # 原始格式
            'profit': -5.0
        },
        {
            'position_id': 'pos_002',
            'member_id': 'user_002',
            'contract_name': 'BTCUSDT',
            'side': 3,  # 开空
            'deal_vol_usdt': 1000.0,
            'timestamp': '2025/7/24 23:10',  # 原始格式
            'profit': 15.0
        },
        {
            'position_id': 'pos_002',
            'member_id': 'user_002',
            'contract_name': 'BTCUSDT',
            'side': 2,  # 平空
            'deal_vol_usdt': 1000.0,
            'timestamp': '2025/7/24 23:16',  # 原始格式
            'profit': -8.0
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    print(f"原始数据:")
    print(df)
    print(f"timestamp列类型: {df['timestamp'].dtype}")
    
    # 创建优化器并测试
    try:
        optimizer = PositionBasedOptimizer()
        
        print(f"\n开始构建完整订单画像...")
        positions = optimizer.build_complete_positions(df)
        
        print(f"构建完成，共 {len(positions)} 个完整订单")
        
        for pos_id, position in positions.items():
            print(f"\n订单 {pos_id}:")
            print(f"  开仓时间: {position.first_open_time} (类型: {type(position.first_open_time)})")
            print(f"  平仓时间: {position.last_close_time} (类型: {type(position.last_close_time)})")
            print(f"  持仓时长: {position.total_duration_minutes:.2f} 分钟")
            print(f"  真实盈亏: {position.real_profit}")
        
        # 测试对敲检测
        print(f"\n开始对敲检测...")
        analysis_result = optimizer.get_comprehensive_analysis()

        print(f"对敲检测完成:")
        if isinstance(analysis_result, dict) and 'results' in analysis_result:
            results = analysis_result['results']
            if isinstance(results, dict):
                print(f"  同账户对敲: {len(results.get('same_account_wash_trading', []))} 个")
                print(f"  跨账户对敲: {len(results.get('cross_account_wash_trading', []))} 个")
            else:
                print(f"  检测结果: {len(results)} 个")
        else:
            print(f"  检测结果: {len(analysis_result) if analysis_result else 0} 个")

        print(f"\n✅ 增量模式时间处理测试成功！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_incremental_mode()
