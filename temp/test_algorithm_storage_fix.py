#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试算法存储管理器的JSON序列化修复
模拟原始错误场景
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import json
from datetime import datetime
from modules.user_analysis.models.user_behavior_models import PositionData
from database.algorithm_storage_manager import json_serializable

def simulate_original_error():
    """模拟原始错误场景"""
    print("🧪 模拟原始错误场景...")
    
    # 创建一个包含PositionData的风险事件数据（模拟原始错误场景）
    position = PositionData(
        position_id="error_pos_001",
        member_id="error_user_001",
        contract_name="BTCUSDT",
        primary_side=1,
        open_time=datetime.now(),
        close_time=datetime.now(),
        duration_minutes=5.0,
        total_open_amount=1000.0,
        total_close_amount=1000.0,
        avg_open_price=50000.0,
        avg_close_price=50000.0,
        total_pnl=0.0,
        total_commission=2.0,
        net_pnl=-2.0,
        leverage=100.0
    )
    
    # 模拟风险事件数据结构
    risk_event = {
        'member_id': 'error_user_001',
        'contract_name': 'BTCUSDT',
        'detection_type': 'wash_trading',
        'detection_method': 'position_based',
        'risk_level': 'high',
        'risk_score': 0.95,
        'abnormal_volume': 1000.0,
        'trade_count': 2,
        'time_range': '2025-08-03 13:00:00 - 2025-08-03 13:05:00',
        'counterparty_ids': ['user_002', 'user_003'],
        # 这里包含PositionData对象，这是导致原始错误的原因
        'position_details': position,
        'related_positions': [position],
        'analysis_metadata': {
            'complete_position': position,
            'risk_factors': ['quick_trade', 'opposite_sides']
        }
    }
    
    print(f"风险事件数据包含PositionData: {type(risk_event['position_details'])}")
    
    # 模拟algorithm_storage_manager中的处理逻辑
    excluded_fields = {
        'member_id', 'memberId', 'user_id', 'contract_name', 'contractName', 'detection_type', 'detection_method',
        'risk_level', 'risk_score', 'total_risk_score', 'abnormal_volume', 'abnormalVolume', 'trading_volume',
        'trade_count', 'transaction_count', 'time_range', 'timeRange', 'hour', 'counterparty_ids'
    }
    
    additional_data = {k: v for k, v in risk_event.items() if k not in excluded_fields}
    print(f"additional_data包含的字段: {list(additional_data.keys())}")
    
    # 测试原始方法（应该失败）
    try:
        original_json = json.dumps(additional_data)
        print("❌ 原始方法不应该成功")
    except Exception as e:
        print(f"✅ 原始方法失败（预期）: {e}")
    
    # 测试修复后的方法
    try:
        fixed_json = json.dumps(json_serializable(additional_data))
        print(f"✅ 修复后的方法成功: {len(fixed_json)} 字符")
        print(f"序列化结果前200字符: {fixed_json[:200]}...")
        
        # 验证反序列化
        deserialized = json.loads(fixed_json)
        print(f"✅ 反序列化成功，包含字段: {list(deserialized.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复后的方法失败: {e}")
        return False

def test_batch_processing():
    """测试批量处理场景"""
    print("\n🧪 测试批量处理场景...")
    
    # 创建多个包含PositionData的风险事件
    risk_events = []
    for i in range(5):
        position = PositionData(
            position_id=f"batch_pos_{i:03d}",
            member_id=f"batch_user_{i:03d}",
            contract_name="BTCUSDT",
            primary_side=1 if i % 2 == 0 else 3,
            open_time=datetime.now(),
            close_time=datetime.now(),
            duration_minutes=float(i * 10 + 5),
            total_open_amount=1000.0 * (i + 1),
            total_close_amount=1000.0 * (i + 1),
            avg_open_price=50000.0,
            avg_close_price=50000.0,
            total_pnl=float(i * 10),
            total_commission=2.0,
            net_pnl=float(i * 10 - 2),
            leverage=10.0 * (i + 1)
        )
        
        risk_event = {
            'member_id': f'batch_user_{i:03d}',
            'contract_name': 'BTCUSDT',
            'detection_type': 'wash_trading',
            'detection_method': 'position_based',
            'risk_level': 'medium',
            'risk_score': 0.7 + i * 0.05,
            'abnormal_volume': 1000.0 * (i + 1),
            'trade_count': i + 2,
            'time_range': f'2025-08-03 13:{i:02d}:00 - 2025-08-03 13:{i+1:02d}:00',
            'counterparty_ids': [f'user_{j:03d}' for j in range(i+1, i+3)],
            'position_details': position,
            'batch_index': i
        }
        
        risk_events.append(risk_event)
    
    print(f"创建了 {len(risk_events)} 个风险事件")
    
    # 模拟批量处理
    successful_count = 0
    failed_count = 0
    
    for idx, risk in enumerate(risk_events):
        try:
            # 模拟algorithm_storage_manager的处理逻辑
            excluded_fields = {
                'member_id', 'contract_name', 'detection_type', 'detection_method',
                'risk_level', 'risk_score', 'abnormal_volume', 'trade_count',
                'time_range', 'counterparty_ids'
            }
            
            additional_data = {k: v for k, v in risk.items() if k not in excluded_fields}
            
            # 使用修复后的序列化方法
            additional_data_json = json.dumps(json_serializable(additional_data))
            
            successful_count += 1
            print(f"✅ 第{idx+1}个风险事件处理成功: {len(additional_data_json)} 字符")
            
        except Exception as e:
            failed_count += 1
            print(f"❌ 第{idx+1}个风险事件处理失败: {e}")
    
    print(f"\n批量处理结果: 成功 {successful_count}/{len(risk_events)}, 失败 {failed_count}")
    
    return failed_count == 0

if __name__ == "__main__":
    print("🚀 开始测试算法存储管理器的JSON序列化修复...")
    
    success1 = simulate_original_error()
    success2 = test_batch_processing()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！算法存储管理器的JSON序列化修复成功。")
        print("🎉 原始错误 'Object of type PositionData is not JSON serializable' 已修复！")
    else:
        print("\n❌ 测试失败，需要进一步检查。")
