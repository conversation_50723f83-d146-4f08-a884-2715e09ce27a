#!/usr/bin/env python3
"""
测试普通模式和增量模式的时间处理一致性
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import pandas as pd
from datetime import datetime
from modules.contract_risk_analysis.services.contract_analyzer import CTContractAnalyzer
from modules.contract_risk_analysis.optimizers.position_based_optimizer import PositionBasedOptimizer

def test_time_consistency():
    """测试普通模式和增量模式的时间处理一致性"""
    
    print("测试普通模式和增量模式的时间处理一致性")
    print("=" * 60)
    
    # 创建测试数据 - 使用原始时间格式
    test_data = [
        {
            'position_id': 'pos_001',
            'member_id': 'user_001',
            'contract_name': 'BTCUSDT',
            'side': 1,  # 开多
            'deal_vol_usdt': 1000.0,
            'createTime': '2025/7/24 23:09',  # 原始格式
            'profit': 10.0
        },
        {
            'position_id': 'pos_001',
            'member_id': 'user_001',
            'contract_name': 'BTCUSDT',
            'side': 4,  # 平多
            'deal_vol_usdt': 1000.0,
            'createTime': '2025/7/24 23:15',  # 原始格式
            'profit': -5.0
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    print(f"原始数据:")
    print(df)
    print(f"createTime列类型: {df['createTime'].dtype}")
    
    # 测试普通模式
    print(f"\n" + "="*30 + " 普通模式测试 " + "="*30)
    try:
        analyzer = CTContractAnalyzer(task_id="test_normal")
        
        # 数据标准化（普通模式的第一步）
        df_standardized = analyzer._standardize_fields(df.copy())
        print(f"普通模式标准化后:")
        print(f"  order_create_time列类型: {df_standardized['order_create_time'].dtype}")
        print(f"  时间样例: {df_standardized['order_create_time'].iloc[0]}")
        
        # 时间派生字段创建
        df_with_time = analyzer._create_time_derived_fields(df_standardized)
        print(f"普通模式时间派生字段创建后:")
        print(f"  order_create_time列类型: {df_with_time['order_create_time'].dtype}")
        print(f"  时间样例: {df_with_time['order_create_time'].iloc[0]}")
        
    except Exception as e:
        print(f"❌ 普通模式测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试增量模式（直接使用优化器）
    print(f"\n" + "="*30 + " 增量模式测试 " + "="*30)
    try:
        optimizer = PositionBasedOptimizer()
        
        # 重命名字段以匹配优化器期望
        df_for_optimizer = df.copy()
        df_for_optimizer['timestamp'] = df_for_optimizer['createTime']
        
        print(f"增量模式输入数据:")
        print(f"  timestamp列类型: {df_for_optimizer['timestamp'].dtype}")
        print(f"  时间样例: {df_for_optimizer['timestamp'].iloc[0]}")
        
        # 构建完整订单画像（这会触发时间处理）
        positions = optimizer.build_complete_positions(df_for_optimizer)
        
        print(f"增量模式处理后:")
        for pos_id, position in positions.items():
            print(f"  订单 {pos_id}:")
            print(f"    开仓时间: {position.first_open_time} (类型: {type(position.first_open_time)})")
            print(f"    平仓时间: {position.last_close_time} (类型: {type(position.last_close_time)})")
        
    except Exception as e:
        print(f"❌ 增量模式测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "="*60)
    print("✅ 时间处理一致性测试完成！")
    print("两种模式都应该能正确处理 '2025/7/24 23:09' 格式的时间")

if __name__ == "__main__":
    test_time_consistency()
